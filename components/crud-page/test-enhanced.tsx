"use client"

// Test component to verify enhanced CRUD page functionality
import { useState } from "react"
import {
  DataPageEnhancedConfig,
  DisplayVariant,
  Grid<PERSON>iew,
  ViewSwitcher,
} from "./index"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface TestItem {
  id: string
  name: string
  email: string
  status: string
  created_date: string
}

// Test custom card component
function TestCard({
  item,
  variant,
  onEdit,
  onDelete,
}: {
  item: TestItem
  variant: DisplayVariant
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
}) {
  const { t } = useLocalization("test_card", locales)
  const cardClass =
    variant === "GRID"
      ? "bg-white rounded-lg shadow-sm border p-4"
      : "bg-white rounded-lg shadow-sm border p-6"

  return (
    <div className={cardClass}>
      <h3
        className={
          variant === "GRID" ? "text-sm font-semibold" : "text-lg font-semibold"
        }
      >
        {item.name}
      </h3>
      <p className="text-gray-600 text-sm">{item.email}</p>
      <p className="text-gray-500 text-xs">{item.status}</p>

      {variant === "CARD" && (
        <p className="text-gray-400 text-xs mt-2">
          {t("created")} {new Date(item.created_date).toLocaleDateString()}
        </p>
      )}

      <div className="flex justify-end space-x-2 mt-3">
        {onEdit && (
          <button
            onClick={() => onEdit(item.id)}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {t("edit_button")}
          </button>
        )}
        {onDelete && (
          <button
            onClick={() => onDelete(item.id)}
            className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
          >
            {t("delete_button")}
          </button>
        )}
      </div>
    </div>
  )
}

// Test data
const testData: TestItem[] = [
  {
    id: "1",
    name: "John Doe",
    email: "<EMAIL>",
    status: "Active",
    created_date: "2024-01-15T10:00:00Z",
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    status: "Inactive",
    created_date: "2024-01-16T11:00:00Z",
  },
  {
    id: "3",
    name: "Bob Johnson",
    email: "<EMAIL>",
    status: "Pending",
    created_date: "2024-01-17T12:00:00Z",
  },
  {
    id: "4",
    name: "Alice Brown",
    email: "<EMAIL>",
    status: "Active",
    created_date: "2024-01-18T13:00:00Z",
  },
]

export default function TestEnhancedCrud() {
  const { t } = useLocalization("test_echanced_crud", locales)
  const [currentVariant, setCurrentVariant] = useState<DisplayVariant>("TABLE")

  const handleEdit = (id: string) => {
    console.log("Edit item:", id)
  }

  const handleDelete = (id: string) => {
    console.log("Delete item:", id)
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">
          {t("enhanced_crud_page_test")}
        </h1>
        <p className="text-gray-600">{t("testing_grid_card")}</p>
      </div>

      <div className="mb-4 flex justify-between items-center">
        <h2 className="text-lg font-semibold">
          {t("test_items")} ({testData.length})
        </h2>
        <ViewSwitcher
          currentVariant={currentVariant}
          onVariantChange={setCurrentVariant}
          allowViewSwitching={true}
        />
      </div>

      {currentVariant === "TABLE" ? (
        <div className="bg-white rounded-lg border">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                  {t("name")}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                  {t("email")}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                  {t("status")}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                  {t("created")}
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                  {t("actions")}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {testData.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 text-sm text-gray-900">
                    {item.name}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-600">
                    {item.email}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-600">
                    {item.status}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-600">
                    {new Date(item.created_date).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(item.id)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {t("edit_button")}
                      </button>
                      <button
                        onClick={() => handleDelete(item.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        {t("delete_button")}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <GridView
          data={testData}
          variant={currentVariant}
          action={{ edit: true, delete: true }}
          onEdit={handleEdit}
          onDelete={handleDelete}
          customRowCard={{
            component: TestCard,
          }}
          currentPage={1}
          totalPages={1}
          onPageChange={() => {}}
        />
      )}
    </div>
  )
}
