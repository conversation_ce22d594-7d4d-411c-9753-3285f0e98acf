import DashboardClientLayout from "@/components/layout/DashboardClientLayout"
import { loadBusinessConfig } from "@/lib/config/business-config"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { buildSessionContextFromCookie } from "../api/sharedFunction"
import { kDEFAULT_LANG } from "../constant"
import { OrganizationProvider } from "@/lib/contexts/OrganizationContext"
import { Organization } from "@/lib/repositories/organizations/interface"

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { accountBusinessLogic, organizationsBusinessLogic } = getBusinessLogics()
  const context = await buildSessionContextFromCookie()

  if (!context) {
    redirect("/auth/login")
  }

  const account = await accountBusinessLogic.getAccount(context)

  if (!account) {
    redirect("/auth/register")
  }

  if (!account.isVerified) {
    redirect("/auth/email-verification")
  }

  // Load organizations for the user
  let organizations: Organization[] = []
  try {
    const orgResult = await organizationsBusinessLogic.getAll({
      page: 1,
      limit: 100,
    }, context)
    organizations = orgResult.items
  } catch (error) {
    console.error("Error loading organizations:", error)
    // Continue without organizations - user can still use the app
  }

  const cookieStore = await cookies()
  const locale = cookieStore.get("locale")?.value || kDEFAULT_LANG
  const businessConfig = await loadBusinessConfig(locale)

  return (
    <OrganizationProvider initialOrganizations={organizations}>
      <DashboardClientLayout account={account} business={businessConfig}>
        {children}
      </DashboardClientLayout>
    </OrganizationProvider>
  )
}
